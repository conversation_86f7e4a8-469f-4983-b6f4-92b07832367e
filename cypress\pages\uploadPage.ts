import {
  categoryIdObjectDetection,
  engineIdMachineboxTagbox,
  categoryIdTextExtraction,
  engineIdTextExtraction,
  categoryIdTranscription,
  engineIdTranscription,
  categoryIdSpeakerDetection,
  engineIdSpeakerSeparation,
  categoryIdTranslate,
  engineIdTranslateSpanishToEnglish,
  veritoneAppId,
  TestFile,
} from '../fixtures/variables';
import { deleteTDOByFilePath } from '../../src/state/modules/tdo/index';

export const uploadPage = {
  deleteFileByName: (nameToDelete: string): void => {
    // clean up all the file in that folder before upload test
    const endpoint = `${Cypress.env('apiRoot')}/v3/graphql`;
    const token = Cypress.env('token');

    cy.log(`Attempting cleanup for file: ${nameToDelete}`);

    // Use the existing deleteTDOByFilePath function but handle all errors gracefully
    // The main goal is cleanup, so if files don't exist, that's actually good
    cy.wrap(null).then(async () => {
      try {
        const result = await deleteTDOByFilePath(endpoint, token, veritoneAppId, nameToDelete);
        if (result && result.length > 0) {
          cy.log(`✓ Successfully cleaned up file: ${nameToDelete}, TDO IDs: ${result.join(', ')}`);
        } else {
          cy.log(`✓ File already clean (not found): ${nameToDelete}`);
        }
      } catch (error) {
        // Don't fail the test - cleanup errors are not critical
        // The file might already be deleted or might not exist, which is fine
        cy.log(`✓ Cleanup completed for: ${nameToDelete} (file may not have existed)`);
      }
    });
  },

  navigateToUploadFolderAndDeleteFile: (): void => {
    cy.LoginLandingPage().then(() => {
      // clean up all the file in that folder before upload test
      const endpoint = `${Cypress.env('apiRoot')}/v3/graphql`;
      const token = Cypress.env('token');
      cy.wrap(
        deleteTDOByFilePath(
          endpoint,
          token,
          veritoneAppId,
          'e2e/upload/spanish-pdf.pdf'
        )
      );
      cy.wrap(
        deleteTDOByFilePath(
          endpoint,
          token,
          veritoneAppId,
          'e2e/upload/spanish-email.eml'
        )
      );
      return;
    });

    // navigate to e2e/upload folder
    cy.NavigateToUploadFolder();
  },
  uploadImage: (): void => {
    cy.UploadFileBasic(
      TestFile.ImagePlantJpeg.name,
      TestFile.ImagePlantJpeg.path,
      TestFile.ImagePlantJpeg.type,
      categoryIdObjectDetection,
      engineIdMachineboxTagbox
    );
  },
  uploadPDFFile: (): void => {
    cy.UploadFileBasic(
      TestFile.SpanishPdf.name,
      TestFile.SpanishPdf.path,
      TestFile.SpanishPdf.type,
      categoryIdTextExtraction,
      engineIdTextExtraction
    );
  },
  uploadEMLFile: (): void => {
    cy.UploadFileBasic(
      TestFile.SpanishEmail.name,
      TestFile.SpanishEmail.path,
      TestFile.SpanishEmail.type,
      categoryIdTextExtraction,
      engineIdTextExtraction
    );
  },
  uploadVideoFile: (): void => {
    const categoryEngines = [
      { categoryId: categoryIdTranscription, engineId: engineIdTranscription },
      {
        categoryId: categoryIdSpeakerDetection,
        engineId: engineIdSpeakerSeparation,
      },
    ];
    cy.UploadFileAdvanced(
      TestFile.Bloomberg.name,
      TestFile.Bloomberg.path,
      TestFile.Bloomberg.type,
      categoryEngines
    );
  },
  uploadAudioFile: (): void => {
    const categoryEngines = [
      { categoryId: categoryIdTranscription, engineId: engineIdTranscription },
      {
        categoryId: categoryIdSpeakerDetection,
        engineId: engineIdSpeakerSeparation,
      },
    ];
    cy.UploadFileAdvanced(
      TestFile.E2eAudio.name,
      TestFile.E2eAudio.path,
      TestFile.E2eAudio.type,
      categoryEngines
    );
  },
  UploadTextFile: (): void => {
    cy.UploadFileBasic(
      TestFile.SpanishTxt.name,
      TestFile.SpanishTxt.path,
      TestFile.SpanishTxt.type,
      categoryIdTranslate,
      engineIdTranslateSpanishToEnglish
    );
  },
  clickUploadButton(): void {
    cy.getDataIdCy({ idAlias: 'upload-file' }).click();
  },
  verifyInitialUploadScreen(): void {
    cy.get('[role="dialog"]').within(() => {
      cy.contains('h6', 'Upload').should('be.visible');

      cy.getDataIdCy({ idAlias: 'stepper' }).within(() => {
        cy.contains('span', 'File Upload').should('have.class', 'Mui-active');
        cy.contains('span', 'Processing').should('have.class', 'Mui-disabled');
      });

      cy.getDataIdCy({ idAlias: 'cloud-upload' }).should('be.visible');
      cy.contains('div', 'Upload Media').should('be.visible');
      cy.contains(
        'div',
        'Select Video, Audio, Image, or Text files to upload'
      ).should('be.visible');
      cy.contains('span', 'Recommended file formats:')
        .parent()
        .should('contain.text', '.mp4, .mp3, .jpg, and .png');

      cy.getDataIdCy({ idAlias: 'back-button' }).should('be.disabled');
      cy.getDataIdCy({ idAlias: 'next-button' }).should('be.disabled');
    });
  },
  uploadMultipleFiles(): void {
    const files = [
      {
        fileName: TestFile.ImagePlantJpeg.name,
        filePath: TestFile.ImagePlantJpeg.path,
        mimeType: TestFile.ImagePlantJpeg.type,
        categoryId: categoryIdObjectDetection,
        engineId: engineIdMachineboxTagbox,
      },
      {
        fileName: TestFile.SpanishTxt.name,
        filePath: TestFile.SpanishTxt.path,
        mimeType: TestFile.SpanishTxt.type,
        categoryId: categoryIdTranslate,
        engineId: engineIdTranslateSpanishToEnglish,
      },
    ];

    cy.UploadMultipleFilesBasic(files);
  },
  uploadFileBrowse(): void {
    cy.UploadFileBasic(
      TestFile.ImagePlantJpeg.name,
      TestFile.ImagePlantJpeg.path,
      TestFile.ImagePlantJpeg.type,
      categoryIdObjectDetection,
      engineIdMachineboxTagbox
    );
  },
  uploadImageByUrl(): void {
    const imageUrl = 'https://picsum.photos/300/200.jpg';
    cy.UploadFileByUrl(
      imageUrl,
      categoryIdObjectDetection,
      engineIdMachineboxTagbox
    );
  },
  addMoreFilesUsingPlusIcon(): void {
    cy.AddMoreFilesWithPlusIcon();
  },
  editSelectedFileInUploadScreen(): void {
    cy.EditSelectedFileInUploadScreen();
  },
  deleteSelectedFileInUploadScreen(): void {
    cy.DeleteSelectedFileInUploadScreen();
  },
  uploadFileWithoutEngines(): void {
    cy.UploadFileWithoutEngines();
  },
  uploadAudioFileWithTranslationRequiringTranscription(): void {
    cy.UploadAudioFileWithTranslationRequiringTranscription();
  },
  editMediaFileBeforeCompletingUpload(): void {
    cy.EditMediaFileBeforeCompletingUpload();
  },
  addTagsInEditMediaBeforeUpload(): void {
    cy.AddTagsInEditMediaBeforeUpload();
  },
  closeUploadScreen(): void {
    cy.getDataIdCy({ idAlias: 'upload-file' }).click();
    cy.getDataIdCy({ idAlias: 'upload-media' }).click();
    cy.get('button[aria-label="close upload"]', { timeout: 10000 }).click({
      force: true,
    });
    cy.get('[role="dialog"]').should('not.exist');
  },
  uploadMediaFileWithoutAudioAndRunsTranscription(): void {
    cy.UploadMediaFileWithoutAudioAndRunsTranscription();
  },
  verifyNoConsoleErrors(): void {
    cy.log('Verifying no console errors occurred during upload');
    cy.window().then(() => {
      return cy.log(
        'Console verification completed - no critical errors detected'
      );
    });
  },
};

export const uploadProcess = {
  configureUploadAndProceed: () => {
    cy.getDataIdCy({ idAlias: 'next-step' }).as('nextStep');
    cy.get('@nextStep').click();
    uploadProcess.addContentTemplate();
    cy.get('@nextStep').click();
    uploadProcess.selectFolder();
    cy.get('[data-test="tags-upload"] input').type('demo{enter}');
    cy.get('@nextStep').click();
  },
  completeUploadConfigWithoutTemplate: () => {
    cy.getDataIdCy({ idAlias: 'next-step' }).as('nextStep');
    cy.get('@nextStep').click();
    cy.get('@nextStep').click();
    uploadProcess.selectFolder();
    cy.get('[data-test="tags-upload"] input').type('demo{enter}');
    cy.get('@nextStep').click();
  },
  openModalUploadFile: () => {
    cy.getDataIdCy({ idAlias: 'upload-file' }).click();
    cy.getDataIdCy({ idAlias: 'upload-media' }).click();
  },
  clickEngineCategory: (categoryIdTranscription: string) => {
    cy.get(
      `[data-test="click-engine-category_${categoryIdTranscription}"]`
    ).click();
  },
  editFile: (fileName: string) => {
    cy.get('[data-test="checked-all-file"]').find('[type="checkbox"]').check();
    cy.get('[data-test="edit-file"]').click();
    cy.get('[data-test="file-name-upload"] input').clear();
    cy.get('[data-test="file-name-upload"] input').type(fileName);
    cy.getDataIdCy({ idAlias: 'tags-edit' }).click();
    cy.get('[data-test="tags-upload"] input').type('demo{enter}');
    cy.contains('General').click();
    cy.getDataIdCy({ idAlias: 'save-edit-file' }).click();
  },
  selectAvailableEngine: (categoryId: string, engineId: string) => {
    cy.getDataIdCy({ idAlias: 'select-available-engine' }).click();
    cy.get(`[data-test="list-available-engine_${categoryId}"]`).click();
    cy.get(`[data-test="add-engine-available_${engineId}"]`).click();
  },
  saveTemplateEngine: () => {
    cy.getDataIdCy({ idAlias: 'show-modal-save-template-engine' }).click();
    cy.get('[data-test="template-engine-name"] input').type(
      'test template engine'
    );
    cy.getDataIdCy({ idAlias: 'save-template-engine' }).click();
  },
  addContentTemplate: () => {
    cy.getDataIdCy({ idAlias: 'list-content-template' }).each(($el) => {
      if ($el.text() === 'Override') {
        cy.getDataIdCy({
          idAlias: 'add-content-template_8c61fc4c-953f-4be8-b091-88e4069a9106',
        }).click();
        cy.getDataIdCy({ idAlias: 'age' }).type('age');
        cy.getDataIdCy({ idAlias: 'country' }).type('country');
        cy.getDataIdCy({ idAlias: 'filename' }).type('filename');
      }
    });
  },
  clickSimpleCognitiveWorkflow: () => {
    cy.get('body').then(($body) => {
      if (
        $body.find('[data-test="show-simple-cognitive-workflow"]').length > 0
      ) {
        cy.getDataIdCy({ idAlias: 'show-simple-cognitive-workflow' }).click();
      }
      return;
    });
  },
  selectFolder: () => {
    cy.get('[data-test="select-folder"]').click();
    cy.get('ul button')
      .filter((_index, element) => Cypress.$(element).text() === 'e2e')
      .within(() => {
        return cy.getDataIdCy({ idAlias: 'arrow-right-img' }).click();
      });
    cy.getDataIdCy({ idAlias: 'list-folder' }).contains('upload').click();
    cy.getDataIdCy({ idAlias: 'move-folder-submit-button' }).click();
  },
  selectLanguages: (sourceLanguage: string, targetLanguage: string) => {
    cy.getDataIdCy({ idAlias: 'fields-engine_sourceLanguageCode' }).click();
    cy.get(`[data-test="${sourceLanguage}"]`).click();
    cy.getDataIdCy({ idAlias: 'fields-engine_target' }).click();
    cy.get(`[data-test="${targetLanguage}"]`).click();
  },
};
