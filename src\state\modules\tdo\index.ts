import fetchGraphQLApi from '../../../helpers/fetchGraphQLApi';
import { getFolderTreeObjectId } from '../folders/folder';

export async function deleteTDOInFolder(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  folderName: string
) {
  if (!folderName) {
    return [];
  }
  const tdos = await getTdosByFolder(
    endpoint,
    token,
    veritoneAppId,
    folderName
  );
  const tdoIds = tdos.map((tdo) => tdo.recording.recordingId);
  return await deleteTDO(endpoint, token, veritoneAppId, tdoIds);
}

export async function deleteTDOByFilePath(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  filePath: string
) {
  if (!filePath) {
    return [];
  }
  const tdo = await getTdoByFilePath(endpoint, token, veritoneAppId, filePath);
  if (!tdo?.recording?.recordingId) {
    return [];
  }
  const tdoIds = [tdo.recording.recordingId];
  return await deleteTDO(endpoint, token, veritoneAppId, tdoIds);
}

export async function getTdoByFilePath(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  filePath: string
) {
  const lastIndex = filePath.lastIndexOf('/');
  let folderName = '';
  let fileName = filePath;
  if (lastIndex > -1) {
    folderName = filePath.slice(0, lastIndex);
    fileName = filePath.slice(lastIndex + 1);
  }
  const treeObjectId = await getFolderTreeObjectId(
    endpoint,
    token,
    veritoneAppId,
    folderName
  );
  const tdos = await getTdosByFolderId(
    endpoint,
    token,
    veritoneAppId,
    treeObjectId
  );
  for (const tdo of tdos) {
    const tdoId = tdo?.recording?.recordingId;
    if (!tdoId) {
      continue;
    }
    if (tdo?.context[0]?.['veritone-file']?.fileName === fileName) {
      return tdo;
    }
  }
  return null;
}

export async function getTdosByFolder(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  folderName: string
) {
  const treeObjectId = await getFolderTreeObjectId(
    endpoint,
    token,
    veritoneAppId,
    folderName
  );
  const results = await getTdosByFolderId(
    endpoint,
    token,
    veritoneAppId,
    treeObjectId
  );
  return results;
}

export async function getTdosByFolderId(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  treeObjectId: string | null
) {
  if (!treeObjectId) {
    return [];
  }

  const query = `
    query searchMedia($search: JSONData!){
      searchMedia(search: $search) {
        jsondata
      }
    }`;

  const variables = {
    search: {
      index: ['mine'],
      select: ['veritone-file'],
      limit: 1000,
      offset: 0,
      query: {
        operator: 'and',
        conditions: [
          {
            field: 'parentTreeObjectIds',
            operator: 'terms',
            values: [treeObjectId],
          },
        ],
      },
    },
  };

  const response = await fetchGraphQLApi<{
    searchMedia: {
      jsondata: {
        results: unknown & { recording: { recordingId: string } }[];
      };
    };
  }>({
    endpoint,
    token,
    veritoneAppId,
    query,
    variables,
  });
  const tdos = response?.data?.searchMedia?.jsondata?.results || [];
  const map = new Map();
  for (const tdo of tdos) {
    const tdoId = tdo?.recording?.recordingId;
    if (!tdoId) {
      continue;
    }
    map.set(tdoId, tdo);
  }
  return Array.from(map.values());
}

export async function deleteTDO(
  endpoint: string,
  token: string,
  veritoneAppId: string,
  tdoIds?: string[]
) {
  if (!tdoIds || !tdoIds.length) {
    return [];
  }
  const deleteTdos = tdoIds.map(
    (tdoId) => `deleteTDO_${tdoId}_0: deleteTDO(id: "${tdoId}") { id message }`
  );
  const query = `
    mutation {
      ${deleteTdos.join('\n')}
  }`;
  const response = await fetchGraphQLApi<{
    [key: string]: { id: string; message: string };
  }>({
    endpoint,
    token,
    veritoneAppId,
    query,
  });
  const deletedTdo = response?.data ? Object.values(response.data) : [];
  return deletedTdo.filter((tdo) => tdo?.id).map((tdo) => tdo.id);
}
