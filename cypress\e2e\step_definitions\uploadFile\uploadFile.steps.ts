import {
  Before,
  DataTable,
  Then,
  When,
} from '@badeball/cypress-cucumber-preprocessor';
import { uploadPage } from '../../../pages/uploadPage';

Before(() => {
  cy.LoginLandingPage();
});

When('Navigate to upload folder and delete file:', (fileList: DataTable) => {
  const files = fileList.hashes();

  cy.log(
    `🧹 Starting cleanup of ${files.length} files: ${files.map((f) => f.filename).join(', ')}`
  );

  // Process cleanup sequentially to avoid overwhelming the API
  files.forEach(({ filename }, index) => {
    if (!filename) {
      throw new Error('Filename is required in DataTable');
    }
    cy.log(`🧹 Cleaning up file ${index + 1}/${files.length}: ${filename}`);
    uploadPage.deleteFileByName(filename);

    // Add a small delay between cleanup operations to prevent API rate limiting
    if (index < files.length - 1) {
      cy.wait(300);
    }
  });

  // Wait a bit before navigating to ensure all cleanup operations are processed
  cy.log('🧹 Cleanup phase completed, navigating to upload folder...');
  cy.wait(1000);
  cy.NavigateToUploadFolder();
});
query { temporalDataObject_3730000056_0:temporalDataObject(id: "3730000056") {
  id
  name
  createdDateTime
  startDateTime
  stopDateTime
  details
  folders {
    treeObjectId
  }
  assets(assetType: "vtn-standard"){
    records{
      id
      signedUri
      assetType
      contentType
    }
  }
  primaryAsset(assetType: "media") {
    id
    signedUri
    contentType
  }
  thumbnailUrl
  sourceImageUrl
  previewUrl
  streams {
    protocol
    uri
  }
}
temporalDataObject_3650000021_1:temporalDataObject(id: "3650000021") {
  id
  name
  createdDateTime
  startDateTime
  stopDateTime
  details
  folders {
    treeObjectId
  }
  assets(assetType: "vtn-standard"){
    records{
      id
      signedUri
      assetType
      contentType
    }
  }
  primaryAsset(assetType: "media") {
    id
    signedUri
    contentType
  }
  thumbnailUrl
  sourceImageUrl
  previewUrl
  streams {
    protocol
    uri
  }
} }

query { temporalDataObject_3750000228_0:temporalDataObject(id: "3750000228") {
  id
  name
  createdDateTime
  startDateTime
  stopDateTime
  details
  folders {
    treeObjectId
  }
  assets(assetType: "vtn-standard"){
    records{
      id
      signedUri
      assetType
      contentType
    }
  }
  primaryAsset(assetType: "media") {
    id
    signedUri
    contentType
  }
  thumbnailUrl
  sourceImageUrl
  previewUrl
  streams {
    protocol
    uri
  }
}
temporalDataObject_3750000227_1:temporalDataObject(id: "3750000227") {
  id
  name
  createdDateTime
  startDateTime
  stopDateTime
  details
  folders {
    treeObjectId
  }
  assets(assetType: "vtn-standard"){
    records{
      id
      signedUri
      assetType
      contentType
    }
  }
  primaryAsset(assetType: "media") {
    id
    signedUri
    contentType
  }
  thumbnailUrl
  sourceImageUrl
  previewUrl
  streams {
    protocol
    uri
  }
}
temporalDataObject_3750000226_2:temporalDataObject(id: "3750000226") {
  id
  name
  createdDateTime
  startDateTime
  stopDateTime
  details
  folders {
    treeObjectId
  }
  assets(assetType: "vtn-standard"){
    records{
      id
      signedUri
      assetType
      contentType
    }
  }
  primaryAsset(assetType: "media") {
    id
    signedUri
    contentType
  }
  thumbnailUrl
  sourceImageUrl
  previewUrl
  streams {
    protocol
    uri
  }
}
temporalDataObject_3750000225_3:temporalDataObject(id: "3750000225") {
  id
  name
  createdDateTime
  startDateTime
  stopDateTime
  details
  folders {
    treeObjectId
  }
  assets(assetType: "vtn-standard"){
    records{
      id
      signedUri
      assetType
      contentType
    }
  }
  primaryAsset(assetType: "media") {
    id
    signedUri
    contentType
  }
  thumbnailUrl
  sourceImageUrl
  previewUrl
  streams {
    protocol
    uri
  }
}
temporalDataObject_3750000224_4:temporalDataObject(id: "3750000224") {
  id
  name
  createdDateTime
  startDateTime
  stopDateTime
  details
  folders {
    treeObjectId
  }
  assets(assetType: "vtn-standard"){
    records{
      id
      signedUri
      assetType
      contentType
    }
  }
  primaryAsset(assetType: "media") {
    id
    signedUri
    contentType
  }
  thumbnailUrl
  sourceImageUrl
  previewUrl
  streams {
    protocol
    uri
  }
}
temporalDataObject_3750000223_5:temporalDataObject(id: "3750000223") {
  id
  name
  createdDateTime
  startDateTime
  stopDateTime
  details
  folders {
    treeObjectId
  }
  assets(assetType: "vtn-standard"){
    records{
      id
      signedUri
      assetType
      contentType
    }
  }
  primaryAsset(assetType: "media") {
    id
    signedUri
    contentType
  }
  thumbnailUrl
  sourceImageUrl
  previewUrl
  streams {
    protocol
    uri
  }
}
temporalDataObject_3750000222_6:temporalDataObject(id: "3750000222") {
  id
  name
  createdDateTime
  startDateTime
  stopDateTime
  details
  folders {
    treeObjectId
  }
  assets(assetType: "vtn-standard"){
    records{
      id
      signedUri
      assetType
      contentType
    }
  }
  primaryAsset(assetType: "media") {
    id
    signedUri
    contentType
  }
  thumbnailUrl
  sourceImageUrl
  previewUrl
  streams {
    protocol
    uri
  }
}
temporalDataObject_3750000221_7:temporalDataObject(id: "3750000221") {
  id
  name
  createdDateTime
  startDateTime
  stopDateTime
  details
  folders {
    treeObjectId
  }
  assets(assetType: "vtn-standard"){
    records{
      id
      signedUri
      assetType
      contentType
    }
  }
  primaryAsset(assetType: "media") {
    id
    signedUri
    contentType
  }
  thumbnailUrl
  sourceImageUrl
  previewUrl
  streams {
    protocol
    uri
  }
}
temporalDataObject_3750000220_8:temporalDataObject(id: "3750000220") {
  id
  name
  createdDateTime
  startDateTime
  stopDateTime
  details
  folders {
    treeObjectId
  }
  assets(assetType: "vtn-standard"){
    records{
      id
      signedUri
      assetType
      contentType
    }
  }
  primaryAsset(assetType: "media") {
    id
    signedUri
    contentType
  }
  thumbnailUrl
  sourceImageUrl
  previewUrl
  streams {
    protocol
    uri
  }
}
temporalDataObject_3750000219_9:temporalDataObject(id: "3750000219") {
  id
  name
  createdDateTime
  startDateTime
  stopDateTime
  details
  folders {
    treeObjectId
  }
  assets(assetType: "vtn-standard"){
    records{
      id
      signedUri
      assetType
      contentType
    }
  }
  primaryAsset(assetType: "media") {
    id
    signedUri
    contentType
  }
  thumbnailUrl
  sourceImageUrl
  previewUrl
  streams {
    protocol
    uri
  }
} }
When('The user opens the upload screen', () => {
  uploadPage.clickUploadButton();
});

Then('The screen is displayed correctly with all default elements', () => {
  uploadPage.verifyInitialUploadScreen();
});

When('The user upload image file for object detection', () => {
  uploadPage.uploadImage();
});

When('The user uploads multiple files for processing', () => {
  uploadPage.uploadMultipleFiles();
});

When('Upload pdf file for text extraction', () => {
  uploadPage.uploadPDFFile();
});

When('Upload eml file for text extraction', () => {
  uploadPage.uploadEMLFile();
});

When('Upload video file for transcription', () => {
  uploadPage.uploadVideoFile();
});

When('Upload audio for transcription', () => {
  uploadPage.uploadAudioFile();
});

When('Upload text file for spanish to english translation', () => {
  uploadPage.UploadTextFile();
});

When('The user uploads a file by browse', () => {
  uploadPage.uploadFileBrowse();
});

When('The user uploads an image by URL', () => {
  uploadPage.uploadImageByUrl();
});

When('The user adds more files using the plus icon', () => {
  uploadPage.addMoreFilesUsingPlusIcon();
});

When('The user edits a selected file in upload screen', () => {
  uploadPage.editSelectedFileInUploadScreen();
});

When('The user deletes a selected file in upload screen', () => {
  uploadPage.deleteSelectedFileInUploadScreen();
});

When('The user uploads a file without running any engines', () => {
  uploadPage.uploadFileWithoutEngines();
});

When(
  'The user uploads audio file with translation that requires transcription',
  () => {
    uploadPage.uploadAudioFileWithTranslationRequiringTranscription();
  }
);

When('The user edits media file before completing upload', () => {
  uploadPage.editMediaFileBeforeCompletingUpload();
});

When('The user adds tags in edit media before upload', () => {
  uploadPage.addTagsInEditMediaBeforeUpload();
});

When('The user closes the upload screen', () => {
  uploadPage.closeUploadScreen();
});

When('The user uploads media file without audio and runs transcription', () => {
  uploadPage.uploadMediaFileWithoutAudioAndRunsTranscription();
});

Then('The console should not log any errors', () => {
  uploadPage.verifyNoConsoleErrors();
});
